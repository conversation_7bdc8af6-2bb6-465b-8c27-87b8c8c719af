import React from 'react';
import { IconReload, IconPlayerPause, IconPlayerPlay, IconDownload } from '@tabler/icons-react';
import { DownloadStatus } from '../types';

interface DownloadButtonProps {
  status: DownloadStatus;
  onPause?: () => void;
  onRetry?: () => void;
  onContinue?: () => void;
  onSave?: () => void;
  disabled?: boolean;
}

const DownloadButton: React.FC<DownloadButtonProps> = ({
  status,
  onPause,
  onRetry,
  onContinue,
  onSave,
  disabled = false
}) => {
  // 根据状态返回对应的按钮配置
  const getButtonConfig = () => {
    switch (status) {
      case DownloadStatus.DOWNLOADING:
        return {
          icon: IconPlayerPause,
          text: '暂停',
          onClick: onPause,
          className: 'flex flex-row justify-center items-center relative gap-2 hover:opacity-90 bg-white rounded-lg border border-gray-200 px-5 py-2.5 w-[94px] h-[43px]',
          iconColor: '#000000',
          textColor: 'text-black'
        };

      case DownloadStatus.ERROR:
        return {
          icon: IconReload,
          text: '重试',
          onClick: onRetry,
          className: 'flex flex-row justify-center items-center relative gap-2 px-5 py-[10px] rounded-lg hover:opacity-90 bg-red-700 w-[92px] h-[41px]',
          iconColor: '#FFFFFF',
          textColor: 'text-white'
        };

      case DownloadStatus.PAUSED:
        return {
          icon: IconPlayerPlay,
          text: '继续',
          onClick: onContinue,
          className: 'flex flex-row justify-center items-center relative gap-2 px-5 py-[10px] rounded-lg hover:opacity-90 bg-blue-700 w-[92px] h-[41px]',
          iconColor: '#FFFFFF',
          textColor: 'text-white'
        };

      case DownloadStatus.COMPLETED:
        return {
          icon: IconDownload,
          text: '保存',
          onClick: onSave,
          className: 'flex flex-row justify-center items-center relative gap-2 px-5 py-[10px] rounded-lg hover:opacity-90 bg-blue-700 w-[92px] h-[41px]',
          iconColor: '#FFFFFF',
          textColor: 'text-white'
        };

      case DownloadStatus.SAVED:
        return {
          icon: IconDownload,
          text: '已保存',
          onClick: onSave,
          className: 'flex flex-row justify-center items-center relative gap-2 hover:opacity-90 bg-white rounded-lg border border-blue-700 px-5 py-2.5 w-[108px] h-[43px]',
          iconColor: '#1A56DB',
          textColor: 'text-blue-700'
        };
    }
  };

  const config = getButtonConfig();
  const IconComponent = config.icon;

  return (
    <button
      onClick={config.onClick}
      disabled={disabled}
      className={`${config.className} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      <IconComponent
        size={16}
        stroke={1.33}
        color={config.iconColor}
        className="flex-none order-0 flex-grow-0"
      />
      <span className={`${config.textColor} font-medium text-sm leading-[150%] order-1 flex-grow-0`}>
        {config.text}
      </span>
    </button>
  );
};

export default DownloadButton;
