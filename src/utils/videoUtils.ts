/**
 * 视频处理工具函数
 */

/**
 * 从 Blob URL 获取视频时长
 * @param blobUrl - 文件的 Blob URL
 * @returns Promise<string | null> - 格式化的时长字符串 (HH:MM:SS) 或 null
 */
export function getVideoDurationFromBlob(blobUrl: string): Promise<string | null> {
  return new Promise((resolve) => {
    try {
      const video = document.createElement('video');

      video.onloadedmetadata = () => {
        try {
          const duration = video.duration;
          if (isNaN(duration) || duration === 0) {
            resolve(null);
          } else {
            resolve(formatDuration(duration));
          }
        } catch {
          resolve(null);
        } finally {
          // 清理资源
          video.src = '';
          video.load();
        }
      };

      video.onerror = () => {
        resolve(null);
      };

      // 设置超时
      setTimeout(() => {
        resolve(null);
      }, 1000); // 5秒超时

      video.src = blobUrl;
      video.load();
    } catch {
      resolve(null);
    }
  });
}

/**
 * 将秒数格式化为 HH:MM:SS 格式
 * @param seconds - 秒数
 * @returns 格式化的时长字符串
 */
export function formatDuration(seconds: number): string {
  if (isNaN(seconds) || seconds < 0) {
    return '00:00:00';
  }

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 分离文件名和后缀名
 * @param filename - 完整文件名
 * @returns { name: string, extension: string } - 分离后的文件名和后缀
 */
export function separateFilenameAndExtension(filename: string): { name: string, extension: string } {
  if (!filename) {
    return { name: '', extension: '' };
  }

  const lastDotIndex = filename.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === 0) {
    // 没有后缀名或者文件名以点开头
    return { name: filename, extension: '' };
  }

  return {
    name: filename.substring(0, lastDotIndex),
    extension: filename.substring(lastDotIndex)
  };
}

/**
 * 组合文件名和后缀名
 * @param name - 文件名（不含后缀）
 * @param extension - 后缀名（包含点）
 * @returns string - 完整文件名
 */
export function combineFilenameAndExtension(name: string, extension: string): string {
  if (!name) return '';
  if (!extension) return name;

  // 确保后缀名以点开头
  const normalizedExtension = extension.startsWith('.') ? extension : '.' + extension;
  return name + normalizedExtension;
}
