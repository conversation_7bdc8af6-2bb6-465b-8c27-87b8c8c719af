import { useCallback } from 'react';
import type { DownloadItem, DownloadProgress, AppState } from '../types';
import { DownloadStatus } from '../types';
import { parseM3u8Content, downloadSegment } from '../utils/m3u8Parser';
import { getVideoDurationFromBlob } from '../utils/videoUtils';

interface UseM3u8DownloadProps {
  state: AppState;
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void;
  setDownloadBlobUrl: (blobUrl: string | null) => void;
  updateDownloadDataDuration: (duration: string | null) => void;
}

/**
 * M3U8下载处理Hook
 * 负责M3U8流媒体文件的下载逻辑
 */
export function useM3u8Download({
  state,
  updateProgress,
  setDownloadBlobUrl,
  updateDownloadDataDuration
}: UseM3u8DownloadProps) {

  // M3U8文件下载处理
  const downloadM3u8File = useCallback(async (item: DownloadItem) => {
    try {
      // 检查是否已保存，避免重复保存
      if (state.savedFiles.has(item.requestId)) {
        console.log(`M3U8文件已保存，跳过重复保存: ${item.filename}`);
        updateProgress(item.requestId, {
          status: DownloadStatus.COMPLETED,
          statusText: '已保存',
          percentage: 100
        });
        return;
      }

      // 第一步：获取M3U8播放列表
      updateProgress(item.requestId, {
        status: DownloadStatus.DOWNLOADING,
        statusText: '获取M3U8播放列表...',
        percentage: 0
      });

      const m3u8Response = await fetch(item.url, {
        headers: item.requestHeaders
          ? Object.fromEntries(
            item.requestHeaders.map((h) => [h.name, h.value])
          )
          : {}
      });

      if (!m3u8Response.ok) {
        throw new Error(`获取M3U8失败: ${m3u8Response.status}`);
      }

      const m3u8Content = await m3u8Response.text();
      console.log('M3U8内容:', m3u8Content);

      // 第二步：解析M3U8内容
      updateProgress(item.requestId, {
        status: DownloadStatus.DOWNLOADING,
        statusText: '解析视频分片...',
        percentage: 0
      });

      let segments = parseM3u8Content(m3u8Content, item.url);

      // 检查是否需要二次解析（主播放列表返回了子播放列表URL）
      if (
        segments.length === 1 &&
        (segments[0].includes('.m3u8') || segments[0].includes('m3u8'))
      ) {
        updateProgress(item.requestId, {
          status: DownloadStatus.DOWNLOADING,
          statusText: '获取子播放列表...',
          percentage: 0
        });

        const subResponse = await fetch(segments[0], {
          headers: item.requestHeaders
            ? Object.fromEntries(
              item.requestHeaders.map((h) => [h.name, h.value])
            )
            : {}
        });

        if (!subResponse.ok) {
          throw new Error(`获取子播放列表失败: ${subResponse.status}`);
        }

        const subContent = await subResponse.text();
        console.log('子播放列表内容:', subContent);
        segments = parseM3u8Content(subContent, segments[0]);
      }

      console.log(`解析到 ${segments.length} 个视频分片`);

      if (segments.length === 0) {
        throw new Error('没有找到视频分片');
      }

      // 第三步：下载所有分片
      const downloadedSegments = await downloadAllSegments(
        segments,
        item,
        updateProgress
      );

      // 第四步：合并分片并创建Blob URL
      await mergeSegments(
        downloadedSegments,
        item,
        updateProgress,
        setDownloadBlobUrl,
        updateDownloadDataDuration
      );

      console.log(`M3U8视频下载完成: ${item.filename}`);
    } catch (error) {
      console.error('M3U8下载失败:', error);
      updateProgress(item.requestId, {
        status: DownloadStatus.ERROR,
        statusText: 'M3U8下载失败: ' + (error as Error).message,
        percentage: 0
      });
      throw error;
    }
  }, [state.savedFiles, updateProgress, setDownloadBlobUrl, updateDownloadDataDuration]);

  return {
    downloadM3u8File
  };
}

/**
 * 下载所有分片
 */
async function downloadAllSegments(
  segments: string[],
  item: DownloadItem,
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void
): Promise<ArrayBuffer[]> {
  const downloadedSegments: ArrayBuffer[] = [];

  for (let i = 0; i < segments.length; i++) {
    updateProgress(item.requestId, {
      status: DownloadStatus.DOWNLOADING,
      statusText: `下载分片 (${i + 1}/${segments.length})`,
      percentage: (i / segments.length) * 90 // 进度条只反映实际下载进度：0% - 90%
    });

    const segmentData = await downloadSegment(
      segments[i],
      item.requestHeaders
    );
    downloadedSegments.push(segmentData);
  }

  return downloadedSegments;
}

/**
 * 合并分片并创建Blob URL
 */
async function mergeSegments(
  downloadedSegments: ArrayBuffer[],
  item: DownloadItem,
  updateProgress: (requestId: string, progress: Partial<DownloadProgress[string]>) => void,
  setDownloadBlobUrl: (blobUrl: string | null) => void,
  updateDownloadDataDuration: (duration: string | null) => void
): Promise<void> {
  // 下载完成，更新进度到100%
  updateProgress(item.requestId, {
    status: DownloadStatus.DOWNLOADING,
    statusText: '合并中...',
    percentage: 100
  });

  // 合并分片
  const mergedBlob = new Blob(downloadedSegments, { type: 'video/mp4' });

  // 创建 Blob URL，等待用户手动保存
  const blobUrl = URL.createObjectURL(mergedBlob);

  // 保存 Blob URL 到状态
  setDownloadBlobUrl(blobUrl);

  // 尝试获取文件时长
  const duration = await getVideoDurationFromBlob(blobUrl);
  if (duration) {
    updateDownloadDataDuration(duration);
    console.log('M3U8文件时长获取成功:', duration);
  }

  // 下载完成，等待用户手动保存
  updateProgress(item.requestId, {
    status: DownloadStatus.COMPLETED,
    statusText: `下载完成，点击保存到本地 (${(mergedBlob.size / 1024 / 1024).toFixed(1)}MB)`,
    percentage: 100
  });
}
