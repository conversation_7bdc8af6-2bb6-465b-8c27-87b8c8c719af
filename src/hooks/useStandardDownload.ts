import { useCallback } from 'react';
import type { DownloadItem, UseStandardDownloadProps } from '../types';
import { DownloadStatus } from '../types';
import {
  downloadFileWithHeaders,
  formatSpeed,
  createDownloadController
} from '../utils/downloadUtils';
import { getVideoDurationFromBlob } from '../utils/videoUtils';

/**
 * 标准文件下载Hook
 * 负责标准媒体文件的下载逻辑
 */
export function useStandardDownload({
  state,
  updateProgress,
  setDownloadController,
  setDownloadBlobUrl,
  updateDownloadDataDuration
}: UseStandardDownloadProps) {

  // 使用下载控制器的标准下载
  const downloadWithController = useCallback(async (downloadData: DownloadItem) => {
    if (!state.pageTaskId) {
      throw new Error('页面任务ID不存在，无法开始下载');
    }

    updateProgress(downloadData.requestId, {
      status: DownloadStatus.DOWNLOADING,
      statusText: '准备下载...',
      percentage: 0
    });

    // 第一步：通知后台设置请求头
    const result = await downloadFileWithHeaders(downloadData, state.pageTaskId);
    console.log(`请求头设置完成: ${result.data?.filename || downloadData.filename}`);

    // 第二步：创建下载控制器
    const controller = createDownloadController({
      requestId: downloadData.requestId,
      url: downloadData.url,
      filename: downloadData.filename || 'download'
    });

    // 设置进度回调
    controller.onProgress = (progress) => {
      updateProgress(downloadData.requestId, {
        status: DownloadStatus.DOWNLOADING,
        statusText: `下载中... ${progress.speed ? formatSpeed(progress.speed) : ''}`,
        percentage: progress.percentage,
        downloadedSize: progress.downloadedSize,
        totalSize: progress.totalSize,
        speed: progress.speed
      });
    };

    // 设置状态变化回调
    controller.onStatusChange = (status) => {
      if (status === 'paused') {
        updateProgress(downloadData.requestId, {
          status: DownloadStatus.PAUSED,
          statusText: '下载已暂停',
          percentage: controller.progress.percentage
        });
      } else if (status === 'downloading') {
        updateProgress(downloadData.requestId, {
          status: DownloadStatus.DOWNLOADING,
          statusText: '下载中...',
          percentage: controller.progress.percentage
        });
      }
    };

    // 保存控制器到状态
    setDownloadController(controller);

    // 开始下载
    const downloadResult = await controller.start();

    if (downloadResult.success && downloadResult.blobUrl) {
      // 下载完成，保存 blob URL
      setDownloadBlobUrl(downloadResult.blobUrl);

      // 尝试获取文件时长
      const duration = await getVideoDurationFromBlob(downloadResult.blobUrl);
      if (duration) {
        updateDownloadDataDuration(duration);
        console.log('文件时长获取成功:', duration);
      }

      updateProgress(downloadData.requestId, {
        status: DownloadStatus.COMPLETED,
        statusText: '下载完成，点击保存到本地',
        percentage: 100
      });
    } else {
      throw new Error(downloadResult.error || '下载失败');
    }
  }, [state.pageTaskId, updateProgress, setDownloadController, setDownloadBlobUrl, updateDownloadDataDuration]);

  // 标准文件下载主函数
  const downloadStandardFile = useCallback(async (downloadData: DownloadItem) => {
    try {
      // 统一使用下载控制器模式
      await downloadWithController(downloadData);
    } catch (error) {
      console.error('标准文件下载失败:', error);
      updateProgress(downloadData.requestId, {
        status: DownloadStatus.ERROR,
        statusText: '下载失败: ' + (error as Error).message,
        percentage: 0
      });
      throw error;
    }
  }, [downloadWithController, updateProgress]);

  return {
    downloadStandardFile,
    downloadWithController
  };
}
